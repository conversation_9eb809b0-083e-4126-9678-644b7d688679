package teach

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"tms/model"
	"tmsc/pkg/cache"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/adapter"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type GrabQuestionParams struct {
	ChapterIDS    []int64 `json:"chapter_ids"`
	QuestionIDS   []int64 `json:"question_ids"`
	CourseIDS     []int64 `json:"course_ids"`
	CoursewareIDS []int64 `json:"coursewares_ids"`
}

func (s *QuestionService) GrabQuestions(userID int64, grabParams *GrabQuestionParams) ([]model.Questions, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	params := adapter.RPCQueryParam{
		Method: "ListQuestionsByConds",
		Params: map[string]any{
			"chapter_ids":    grabParams.ChapterIDS,
			"course_ids":     grabParams.CourseIDS,
			"courseware_ids": grabParams.CoursewareIDS,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}

	questions := make([]model.Questions, 0)
	if err := json.Unmarshal(queryData, &questions); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return questions, nil
}

func (s *QuestionService) GrabQuestionExts(userID int64, grabParams *GrabQuestionParams) ([]model.QuestionsExt, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}

	params := adapter.RPCQueryParam{
		Method: "ListQuestionExts",
		Params: map[string]any{
			"question_ids": grabParams.QuestionIDS,
			"user_id":      userID,
			"chapter_ids":  grabParams.ChapterIDS,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}
	var questionsExt []model.QuestionsExt
	if err := json.Unmarshal(queryData, &questionsExt); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}
	return questionsExt, nil
}

func (s *QuestionService) UpsertData(tx *gorm.DB, userID int64, questions []model.Questions, questionsExt []model.QuestionsExt) error {
	if len(questions) == 0 || userID == 0 || len(questionsExt) == 0 {
		logger.Logger.Info("No data to upsert", zap.Int64("userID", userID))
		return nil
	}
	var questionIDS []int64
	for _, q := range questions {
		questionIDS = append(questionIDS, q.ID)
	}
	if err := tx.Where("id IN (?)", questionIDS).Delete(&model.Questions{}).Error; err != nil {
		return err
	}
	if err := tx.CreateInBatches(&questions, 100).Error; err != nil {
		return err
	}

	if len(questionsExt) > 0 {
		if err := tx.Where("question_id IN (?)", questionIDS).Delete(&model.QuestionsExt{}).Error; err != nil {
			return err
		}
		if err := tx.CreateInBatches(&questionsExt, 100).Error; err != nil {
			return err
		}
	}

	return nil
}

// GetQuestionsByChapterID 根据章节ID获取关联的试题列表（不分页）
func (s *QuestionService) GetQuestionsByChapterID(chapterID int64) ([]model.Questions, error) {
	if chapterID == 0 {
		return nil, errors.New("无效的章节ID")
	}

	var questionIDs []int64
	// 查询该章节下所有关联的试题ID（来自 questions_ext 表）
	err := db.DB.Model(&model.QuestionsExt{}).
		Where("ext_key = ? AND ext_value = ?", "chapter", chapterID).
		Distinct("question_id").
		Pluck("question_id", &questionIDs).Error

	if err != nil {
		return nil, errors.New("查询试题ID失败: " + err.Error())
	}

	if len(questionIDs) == 0 {
		return []model.Questions{}, nil
	}

	// 查询试题详情
	var questions []model.Questions
	err = db.DB.Model(&model.Questions{}).
		Where("id IN (?)", questionIDs).
		Find(&questions).Error

	if err != nil {
		return nil, errors.New("查询试题失败: " + err.Error())
	}

	return questions, nil
}

func (api *QuestionService) GetQuestionsByCondition(req model.ReqQuestionSearch) ([]model.Questions, int64, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	// 构建试题查询
	dbQuery := db.DB.Model(&model.Questions{})

	// 根据条件构建关联查询
	if req.CourseID > 0 {
		// 通过课程ID检索：关联课件表，再通过课件关联试题
		dbQuery = dbQuery.
			Joins("JOIN questions_ext qe ON questions.id = qe.question_id AND qe.ext_key = 'courseware'").
			Joins("JOIN courseware cw ON qe.ext_value = cw.id").
			Where("cw.course_id = ?", req.CourseID)
		if req.CoursewareID > 0 {
			dbQuery = dbQuery.Where("cw.id = ?", req.CoursewareID)
		}
	} else if req.ChapterID > 0 {
		// 通过章节ID检索：直接关联章节扩展表
		dbQuery = dbQuery.
			Joins("JOIN questions_ext qe ON questions.id = qe.question_id AND qe.ext_key = 'chapter'").
			Where("qe.ext_value = ?", req.ChapterID)
	} else {
		return nil, 0, errors.New("必须提供课程ID或章节ID进行检索")
	}

	// 附加其他过滤条件
	if req.Status == "" {
		req.Status = model.QuestionStatusPublished
	}
	dbQuery = dbQuery.Where("questions.status = ?", req.Status)
	if req.UserID > 0 {
		dbQuery = dbQuery.Where("questions.user_id = ?", req.UserID)
	}
	if req.Name != "" {
		dbQuery = dbQuery.Where("questions.title ILIKE ?", "%"+req.Name+"%")
	}

	// 查询总数
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.New("查询总数失败: " + err.Error())
	}

	// 分页查询试题列表
	var questions []model.Questions
	offset := (req.Page - 1) * req.PageSize
	err := dbQuery.Offset(offset).
		Limit(req.PageSize).
		Order("questions.created_at DESC").
		Find(&questions).Error

	if err != nil {
		return nil, 0, errors.New("查询试题失败: " + err.Error())
	}

	return questions, total, nil
}
