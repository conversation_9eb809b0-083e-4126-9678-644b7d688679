package teach

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
	"tms/model"
	"tmsc/pkg/config"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/utils"

	"github.com/spf13/cast"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func CoursewareDownloadDir(chapterID int64) string {
	return filepath.Join("data", "downloads", "coursewares", cast.ToString(chapterID))
}

func ScormUnzipDir(chapterID int64, coursewareID int64) string {
	return filepath.Join("data", "scorm", cast.ToString(chapterID), cast.ToString(coursewareID))
}

// 根据课件id列表，解压scorm包
func (s *CoursewareService) unzipScormByCoursewareIDs(coursewareIDS []int64) error {
	var coursewares []model.Courseware
	if err := db.DB.Where("id IN (?)", coursewareIDS).Find(&coursewares).Error; err != nil {
		return errors.New("获取数据失败: " + err.Error())
	}

	for _, courseware := range coursewares {
		distFloder := ScormUnzipDir(courseware.ChapterID, courseware.ID)
		// courseware.FilePath = ./uploads/courseware/Quiz_test.zip
		// 包下载到本地
		remote := strings.TrimSuffix(config.Config.Remote.URL, "api")
		remoteFilePath := strings.TrimPrefix(courseware.FilePath, "./")
		remoteFilePath = remote + remoteFilePath
		var downloadFilePath string

		// 确定下载文件的保存路径
		downloadDir := CoursewareDownloadDir(courseware.ChapterID)
		downloadFilePath = filepath.Join(downloadDir, filepath.Base(courseware.FilePath))

		// 下载文件
		logger.Logger.Info("开始下载课件", zap.String("URL", remoteFilePath), zap.String("保存路径", downloadFilePath))
		if err := utils.DownloadFile(remoteFilePath, downloadFilePath); err != nil {
			logger.Logger.Error("下载课件失败", zap.String("URL", remoteFilePath), zap.Error(err))
			return fmt.Errorf("下载课件失败: %w", err)
		}

		logger.Logger.Info("课件下载成功", zap.String("保存路径", downloadFilePath))

		fileInfo, err := os.Stat(downloadFilePath)
		if err != nil {
			logger.Logger.Error("获取下载文件信息失败", zap.String("文件路径", downloadFilePath), zap.Error(err))
			return fmt.Errorf("获取下载文件信息失败: %w", err)
		}

		logger.Logger.Info("下载文件大小", zap.String("文件路径", downloadFilePath), zap.Int64("大小(字节)", fileInfo.Size()))

		unzipPath, err := utils.UnzipScorm(downloadFilePath, distFloder)
		if err != nil {
			logger.Logger.Error("解压课件失败", zap.String("文件路径", downloadFilePath), zap.Error(err))
			return errors.New("解压失败: " + err.Error())
		}

		logger.Logger.Info("课件解压成功", zap.String("保存路径", unzipPath))

		if err := db.DB.Model(&model.Courseware{}).Where("id = ?", courseware.ID).Update("unzip_path", unzipPath).Error; err != nil {
			return errors.New("更新数据失败: " + err.Error())
		}
	}

	return nil
}

func (s *CoursewareService) DownloadAndUnzipCourseware(ids []int64) error {
	// 将课件下载解压操作放入后台协程，避免阻塞主请求
	go func(coursewareIDs []int64) {
		// 定义并发限制
		concurrencyLimit := 5
		sem := make(chan struct{}, concurrencyLimit)
		var wg sync.WaitGroup

		for _, coursewareID := range coursewareIDs {
			wg.Add(1)
			sem <- struct{}{}
			go func(id int64) {
				defer wg.Done()
				defer func() { <-sem }()

				if err := s.unzipScormByCoursewareIDs([]int64{id}); err != nil {
					logger.Logger.Error("解压课件失败", zap.Error(err))
				}
			}(coursewareID)
		}
		wg.Wait()
		logger.Logger.Info("所有课件解压完成")
	}(ids)

	return nil
}

func (s *CoursewareService) CheckCoursewareExists(id int64) (bool, error) {
	var courseware model.Courseware
	err := db.DB.Where("id = ?", id).First(&courseware).Error
	if err != nil {
		return false, err
	}
	// build path
	downloadDir := CoursewareDownloadDir(courseware.ChapterID)
	downloadFilePath := filepath.Join(downloadDir, filepath.Base(courseware.FilePath))
	// check file exists
	if _, err := os.Stat(downloadFilePath); os.IsNotExist(err) {
		return false, nil
	}

	// check unzip
	if courseware.UnzipPath == "" {
		return false, nil
	}
	if _, err := os.Stat(courseware.UnzipPath); os.IsNotExist(err) {
		return false, nil
	}

	return true, nil
}

func (s *CoursewareService) ClearCourseCache(courseID int64) error {
	var courseware model.Courseware
	err := db.DB.Where("id = ?", courseID).First(&courseware).Error
	if err != nil {
		return err
	}

	// build path
	downloadDir := CoursewareDownloadDir(courseware.ChapterID)
	downloadFilePath := filepath.Join(downloadDir, filepath.Base(courseware.FilePath))
	// 删除文件
	if _, err := os.Stat(downloadFilePath); err == nil {
		if err := os.Remove(downloadFilePath); err != nil {
			return errors.New("删除文件失败: " + err.Error())
		}
	} else if !os.IsNotExist(err) {
		return errors.New("检查文件状态失败: " + err.Error())
	}
	logger.Logger.Info("课件下载包删除成功", zap.String("文件路径", downloadFilePath))

	// build unzip path
	unzipPath := filepath.Join("data", "scorm", cast.ToString(courseware.ChapterID), cast.ToString(courseware.ID))
	if err := os.RemoveAll(unzipPath); err != nil {
		return err
	}
	logger.Logger.Info("课件解压包删除成功", zap.String("文件路径", unzipPath))

	return nil
}

// 虚拟课件初始化
func (s *CoursewareService) InitializeVirtualCourseware(userID, coursewareID int64, subtype int) (int64, error) {
	// 查找现有的未完成记录
	var history model.VirtualCoursewareSubmitHistory
	err := db.DB.Where("user_id = ? AND courseware_id = ? AND subtype = ? AND status = ?",
		userID, coursewareID, subtype, model.VirtualCoursewareStatusIncomplete.String()).
		First(&history).Error

	if err == nil {
		// 找到现有记录，直接返回ibs.Success(c, "初始化成功", gin.H{"id": history.ID})
		return history.ID, nil
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Error("查询历史记录失败", zap.Error(err))
		return 0, err
	}

	// 创建新记录
	now := time.Now().Unix()
	newHistory := model.VirtualCoursewareSubmitHistory{
		UserID:       userID,
		CoursewareID: coursewareID,
		Subtype:      subtype,
		Score:        100,
		Status:       model.VirtualCoursewareStatusIncomplete.String(),
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	if err := db.DB.Create(&newHistory).Error; err != nil {
		return 0, err
	}
	return newHistory.ID, nil
}

// 虚拟课件答题历史
func (s *CoursewareService) GetVirtualCoursewareAnswerHistory(id int64) (*model.VirtualCoursewareSubmitHistory, error) {
	var history model.VirtualCoursewareSubmitHistory
	err := db.DB.Where("id = ?", id).First(&history).Error
	if err != nil {
		return nil, err
	}

	return &history, nil
}

// 虚拟课件提交
func (s *CoursewareService) SubmitVirtualCoursewareAnswer(historyID int64, step uint32, score uint32, totalStep uint32) error {
	var history model.VirtualCoursewareSubmitHistory
	if err := db.DB.First(&history, historyID).Error; err != nil {
		return err
	}

	history.Step = step
	history.Score = score
	now := time.Now().Unix()
	history.TotalTime = now - history.CreatedAt
	history.UpdatedAt = now
	if totalStep == (step + 1) {
		history.Status = model.VirtualCoursewareStatusCompleted.String()
		history.CompletedAt = now
	}
	if err := db.DB.Save(&history).Error; err != nil {
		return err
	}

	return nil
}
