package user

import (
	"os"
	"path/filepath"
	"sync"
	"tms/model"
	"tmsc/libs"
	"tmsc/middleware"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/auth"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

type VirtualCoursewareAPI struct{}

func init() {
	api := &VirtualCoursewareAPI{}

	v1 := middleware.NewGroup("v1", "API V1 版本")
	usersV1 := v1.Group("user", "客户端用户接口")
	coursewareGroup := usersV1.Group("virtual_courseware", "虚拟课件接口")
	{
		coursewareGroup.Register("POST", "/initialize", api.Initialize, model.AuthUser, "虚拟课件初始化")
		coursewareGroup.Register("POST", "/test/initialize", api.TestInitialize, "guest", "虚拟课件初始化")
		coursewareGroup.Register("GET", "/answer/history", api.GetAnswerHistory, "guest", "答题历史")
		coursewareGroup.Register("POST", "/answer/submit", api.SubmitAnswer, "guest", "提交答题")
		coursewareGroup.Register("POST", "/navigate", api.Navigate, model.AuthUser, "导航")
		coursewareGroup.Register("GET", "/answer/detail/:id", api.GetAnswerHistoryDetail, model.AuthUser, "答题历史详情")
	}
}

func (api *VirtualCoursewareAPI) TestInitialize(c *gin.Context) {
	logger.Logger.Info("虚拟课件初始化")
	var req struct {
		CoursewareID int64 `json:"courseware_id" binding:"required"`
		Subtype      int   `json:"subtype" binding:"min=0"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Logger.Error("初始化失败", zap.Error(err))
		libs.Error(c, "初始化失败")
		return
	}

	id, err := coursewareService.InitializeVirtualCourseware(1, req.CoursewareID, req.Subtype)
	if err != nil {
		logger.Logger.Error("初始化失败", zap.Error(err))
		libs.Error(c, "初始化失败")
		return
	}

	libs.Success(c, "初始化成功", gin.H{"id": id})

}

func (api *VirtualCoursewareAPI) Initialize(c *gin.Context) {
	logger.Logger.Info("虚拟课件初始化")
	var req struct {
		CoursewareID int64 `json:"courseware_id" binding:"required"`
		Subtype      int   `json:"subtype" binding:"min=0"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Logger.Error("初始化失败", zap.Error(err))
		libs.Error(c, "初始化失败")
		return
	}
	claims := auth.GetUserClaims(c)
	if claims != nil && claims.ID == 0 {
		libs.Error(c, "初始化失败")
		return
	}

	id, err := coursewareService.InitializeVirtualCourseware(claims.ID, req.CoursewareID, req.Subtype)
	if err != nil {
		logger.Logger.Error("初始化失败", zap.Error(err))
		libs.Error(c, "初始化失败")
		return
	}

	libs.Success(c, "初始化成功", gin.H{"id": id})
}

func (api *VirtualCoursewareAPI) GetAnswerHistory(c *gin.Context) {
	logger.Logger.Info("获取答题历史")
	idStr := c.Query("id")
	if idStr == "" {
		libs.Error(c, "参数错误")
		return
	}
	historyID := cast.ToInt64(idStr)
	data, err := coursewareService.GetVirtualCoursewareAnswerHistory(historyID)
	if err != nil {
		logger.Logger.Error("获取答题历史失败", zap.Error(err))
		libs.Error(c, "获取答题历史失败")
		return
	}

	if data.Step != 0 {
		data.Step += 1
	}

	libs.Success(c, "success", data)
}

func (api *VirtualCoursewareAPI) SubmitAnswer(c *gin.Context) {
	logger.Logger.Info("提交虚拟课件数据")
	var req struct {
		ID        int64  `json:"id" binding:"required"`
		UserID    int64  `json:"user_id" binding:"required"`
		Subtype   int    `json:"subtype" binding:"min=0"`
		Step      uint32 `json:"step" binding:"min=0"`
		Score     uint32 `json:"score" binding:"min=0"`
		TotalStep uint32 `json:"total_step" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Logger.Error("提交答题失败", zap.Error(err))
		libs.Error(c, "提交答题失败")
		return
	}

	data, err := coursewareService.GetVirtualCoursewareAnswerHistory(req.ID)
	if err != nil {
		logger.Logger.Error("获取答题历史失败", zap.Error(err))
		libs.Error(c, "获取答题历史失败")
		return
	}

	err = coursewareService.SubmitVirtualCoursewareAnswer(req.ID, req.Step, req.Score, req.TotalStep)
	if err != nil {
		logger.Logger.Error("提交答题失败", zap.Error(err))
		libs.Error(c, "提交答题失败")
		return
	}

	if req.TotalStep > 0 {
		// TODO: UPDATE courseware total_step
		logger.Logger.Info("提交答题完成", zap.Int64("user_id", req.UserID), zap.Int64("courseware_id", data.CoursewareID))
	}

	libs.Success(c, "提交成功", nil)
}

func (api *VirtualCoursewareAPI) Navigate(c *gin.Context) {
	var req struct {
		CoursewareID int64 `json:"courseware_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Logger.Error("导航失败", zap.Error(err))
		libs.Error(c, "导航失败")
		return
	}

	var courseware model.Courseware
	if err := db.DB.Where("id = ? AND courseware_type = ?", req.CoursewareID, model.CoursewareTypeVirtual).First(&courseware).Error; err != nil {
		logger.Logger.Error("导航失败", zap.Error(err))
		libs.Error(c, "导航失败")
		return
	}

	// 查看是否解压
	exist, err := coursewareService.CheckCoursewareExists(courseware.ID)
	if err != nil {
		logger.Logger.Error("导航失败", zap.Error(err))
		libs.Error(c, "导航失败")
		return
	}
	logger.Logger.Info("课件是否存在", zap.Bool("exist", exist))
	var wg sync.WaitGroup
	var downloadErr error
	if !exist {
		// 不存在，必须同步等待下载解压完成
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := coursewareService.DownloadAndUnzipCourseware([]int64{courseware.ID}); err != nil {
				logger.Logger.Error("导航失败", zap.Error(err))
				downloadErr = err
			}
		}()
	}
	wg.Wait()
	if downloadErr != nil {
		libs.Error(c, "导航失败")
		return
	}

	distDir := filepath.Join("data", "scorm", cast.ToString(courseware.ChapterID), cast.ToString(courseware.ID))

	// 递归查找 distDir 下的 index.html 文件
	var indexPath string
	err = filepath.Walk(distDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && info.Name() == "index.html" {
			// 找到第一个 index.html 就停止
			indexPath = path
			return filepath.SkipDir // 找到后跳过当前目录，避免继续遍历
		}
		return nil
	})

	if err != nil {
		logger.Logger.Error("查找 index.html 失败", zap.Error(err))
		libs.Error(c, "查找 index.html 失败")
		return
	}

	if indexPath == "" {
		logger.Logger.Error("未找到 index.html 文件", zap.String("distDir", distDir))
		libs.Error(c, "未找到 index.html 文件")
		return
	}

	// 计算相对于 distDir 的路径，这会自动去除 "data/scorm/id/id" 前缀
	relPath, err := filepath.Rel(distDir, indexPath)
	if err != nil {
		logger.Logger.Error("计算相对路径失败", zap.Error(err))
		libs.Error(c, "计算相对路径失败")
		return
	}

	libs.Success(c, "success", relPath)
}

func (api *VirtualCoursewareAPI) GetAnswerHistoryDetail(c *gin.Context) {
	historyID := cast.ToInt64(c.Param("id"))
	data, err := coursewareService.GetVirtualCoursewareAnswerHistory(historyID)
	if err != nil {
		logger.Logger.Error("获取答题历史失败", zap.Error(err))
		libs.Error(c, "获取答题历史失败")
		return
	}

	libs.Success(c, "success", data)
}
