package user

import (
	"fmt"
	"tms/model"
	"tmsc/libs"
	"tmsc/middleware"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/auth"
	"tmsc/services/teach"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type TeachAPI struct{}

var teachService = teach.NewTeachService()
var courseService = teach.NewCourseSerivce()
var coursewareService = teach.NewCoursewareService()

func init() {
	teachAPI := &TeachAPI{}

	v1 := middleware.NewGroup("v1", "API V1 版本")
	usersV1 := v1.Group("user", "客户端用户接口")
	teachPlanV1 := usersV1.Group("teach_plan", "教学计划接口")
	{
		teachPlanV1.Register("POST", "/grab", teachAPI.GrabTechPlan, "user", "远程教学计划同步")
		// teachPlanV1.Register("POST", "/list", teachAPI.GetTechPlanList, "user", "教学计划列表")
		// teachPlanV1.Register("GET", "/content/:id", teachAPI.GetTechPlanContent, "user", "教学计划内容")
		// teachPlanV1.Register("POST", "/courses", teachAPI.GetCoursesOfTech, "user", "教学计划下的课程列表")
	}
}

func (api *TeachAPI) GrabTechPlan(c *gin.Context) {
	claims := auth.GetUserClaims(c)
	plansWithExt, err := teachService.GrabTechPlan(claims.ID)
	if err != nil {
		logger.Logger.Error("获取远程教学计划失败", zap.Error(err))
		libs.Error(c, "同步教学计划失败")
		return
	}
	if len(plansWithExt) == 0 {
		logger.Logger.Error("获取远程教学计划失败", zap.Error(fmt.Errorf("未获取到教学计划")))
		libs.Success(c, "同步教学计划成功", nil)
		return
	}
	var plans []model.TeachingPlan
	var planExts []model.TeachingPlanExt
	for _, planWithExt := range plansWithExt {
		plans = append(plans, planWithExt.Plan)
		planExts = append(planExts, planWithExt.Exts...)
	}

	// 获取课程列表
	courses, err := courseService.GrabCourses(claims.ID, plans)
	if err != nil {
		logger.Logger.Error("获取远程课程失败", zap.Error(err))
		libs.Error(c, "同步教学计划失败")
		return
	}

	coursesIDS := make([]int64, 0)
	for _, course := range courses {
		coursesIDS = append(coursesIDS, course.ID)
	}

	// 获取章节
	chapters, err := chapterService.GrabChapters(claims.ID, coursesIDS...)
	if err != nil {
		logger.Logger.Error("获取远程章节失败", zap.Error(err))
		libs.Error(c, "同步教学计划失败")
		return
	}

	chapterIDS := make([]int64, 0)
	for _, chapter := range chapters {
		chapterIDS = append(chapterIDS, chapter.ID)
	}

	// 获取课件
	coursewaresWithExt, err := chapterService.GrabCoursewares(claims.ID, chapterIDS...)
	if err != nil {
		logger.Logger.Error("获取远程课件失败", zap.Error(err))
		libs.Error(c, "同步教学计划失败")
		return
	}

	var coursewareIDS []int64
	for _, courseware := range coursewaresWithExt {
		coursewareIDS = append(coursewareIDS, courseware.Courseware.ID)
	}

	if err := coursewareService.DownloadAndUnzipCourseware(coursewareIDS); err != nil {
		logger.Logger.Error("下载课件失败", zap.Error(err))
	}

	grabQuestionParam := &teach.GrabQuestionParams{
		ChapterIDS: chapterIDS,
	}
	// 获取章节下的习题
	questions, err := questionService.GrabQuestions(claims.ID, grabQuestionParam)
	if err != nil {
		logger.Logger.Error("获取远程习题失败", zap.Error(err))
		libs.Error(c, "同步教学计划失败")
		return
	}

	grabQuestionParam.QuestionIDS = make([]int64, 0)
	for _, question := range questions {
		grabQuestionParam.QuestionIDS = append(grabQuestionParam.QuestionIDS, question.ID)
	}

	// 获取习题ext
	questionExts, err := questionService.GrabQuestionExts(claims.ID, grabQuestionParam)
	if err != nil {
		logger.Logger.Error("GrabQuestionExts 查询试题失败", zap.Error(err))
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	// 获取章节下的资料分类和资料
	categories, err := resourceService.GrabResourceCategory(claims.ID, nil)
	if err != nil {
		logger.Logger.Error("获取资料分类失败", zap.Error(err))
		libs.Error(c, "获取资料分类失败")
		return
	}
	grabResourceParam := map[string]any{
		"chapter_ids": chapterIDS,
	}
	resources, err := resourceService.GrabResource(claims.ID, grabResourceParam)
	if err != nil {
		logger.Logger.Error("获取资料列表失败", zap.Error(err))
		libs.Error(c, "获取资料列表失败")
		return
	}

	err = db.DB.Transaction(func(tx *gorm.DB) error {
		// 保存教学计划和课程,章节和课件
		if err := teachService.UpsertPlans(tx, plans); err != nil {
			return err
		}

		if err := teachService.UpsertPlanExts(tx, plans, planExts); err != nil {
			return err
		}

		if err := teachService.UpsertCourses(tx, plans, courses); err != nil {
			return err
		}

		if err := teachService.UpsertChapters(tx, courses, chapters); err != nil {
			return err
		}

		if err := teachService.UpsertCoursewares(tx, chapters, coursewaresWithExt); err != nil {
			return err
		}

		if err := teachService.UpsertQuestions(tx, chapters, questions, questionExts); err != nil {
			return err
		}

		// 同步资料
		if err := resourceService.UpsertData(tx, claims.ID, categories, resources); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		logger.Logger.Error("同步教学计划失败", zap.Error(err))
		libs.Error(c, "同步教学计划失败")
		return
	}

	libs.Success(c, "同步教学计划成功", nil)
}

func (api *TeachAPI) GetTechPlanList(c *gin.Context) {
	claims := auth.GetUserClaims(c)
	req := model.ReqTeachingPlanSearch{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Logger.Error("获取教学计划列表失败", zap.Error(err))
		libs.Error(c, "获取教学计划列表失败")
		return
	}
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	data, err := teachService.GetTechPlanList(req, claims.ID)
	if err != nil {
		logger.Logger.Error("获取教学计划列表失败", zap.Error(err))
		libs.Error(c, "获取教学计划列表失败")
		return
	}

	list := libs.GeneratePageResult(data.List, data.Total, libs.PageParam{
		Page:     req.Page,
		PageSize: req.PageSize,
	})

	libs.Success(c, "获取教学计划列表成功", list)
}

func (api *TeachAPI) GetTechPlanContent(c *gin.Context) {
	planID := cast.ToInt64(c.Param("id"))
	data, err := teachService.GetTechPlanContent(planID)
	if err != nil {
		logger.Logger.Error("获取教学计划内容失败", zap.Error(err))
		libs.Error(c, "获取教学计划内容失败")
		return
	}

	libs.Success(c, "获取教学计划内容成功", data)
}

func (api *TeachAPI) GetCoursesOfTech(c *gin.Context) {
	var req model.ReqCoursesSearch
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	if req.PlanID == 0 {
		libs.Error(c, "参数错误")
		return
	}
	claims := auth.GetUserClaims(c)
	data, total, err := courseService.GetTechCourses(req, claims.ID)
	if err != nil {
		logger.Logger.Error("获取课程列表失败", zap.Error(err))
		libs.Error(c, "获取课程列表失败")
		return
	}

	result := libs.GeneratePageResult(data, total, libs.PageParam{
		Page:     req.Page,
		PageSize: req.PageSize,
	})
	libs.Success(c, "获取课程列表成功", result)
}
