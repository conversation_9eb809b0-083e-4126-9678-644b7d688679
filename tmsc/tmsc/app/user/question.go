package user

import (
	"tms/model"
	"tmsc/libs"
	"tmsc/middleware"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/auth"
	"tmsc/services/teach"
	"tmsc/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type QuestionAPI struct{}

func init() {
	api := &QuestionAPI{}

	v1 := middleware.NewGroup("v1", "API V1 版本")
	usersV1 := v1.Group("user", "客户端用户接口")
	questionGroup := usersV1.Group("question", "试题接口")
	{
		questionGroup.Register("POST", "/grab", api.GrabQuestions, model.AuthUser, "同步试题")
		questionGroup.Register("GET", "/list", api.GetQuestionList, model.AuthUser, "获取试题列表")
	}
}

func (api *QuestionAPI) GrabQuestions(c *gin.Context) {
	var req struct {
		CourseID     int64 `json:"course_id" binding:"required"`
		CoursewareID int64 `json:"courseware_id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	claims := auth.GetUserClaims(c)
	grabQuestionParam := &teach.GrabQuestionParams{
		CourseIDS:     []int64{req.CourseID},
		CoursewareIDS: []int64{req.CoursewareID},
	}

	questions, err := questionService.GrabQuestions(claims.ID, grabQuestionParam)
	if err != nil {
		logger.Logger.Error("获取远程习题失败", zap.Error(err))
		libs.Error(c, "同步试题失败")
		return
	}

	var questionIDS []int64
	for _, v := range questions {
		questionIDS = append(questionIDS, v.ID)
	}

	// 获取ext
	questionExts, err := questionService.GrabQuestionExts(claims.ID, &teach.GrabQuestionParams{
		QuestionIDS: questionIDS,
	})
	if err != nil {
		logger.Logger.Error("GrabQuestionExts 查询试题失败", zap.Error(err))
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	err = utils.Transactional(db.DB, func(tx *gorm.DB) error {
		if err := questionService.UpsertData(tx, claims.ID, questions, questionExts); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		logger.Logger.Error("UpsertData 同步试题失败", zap.Error(err))
		libs.Error(c, "同步失败: "+err.Error())
		return
	}

	libs.Success(c, "同步成功", nil)
}

func (api *QuestionAPI) GetQuestionList(c *gin.Context) {
	var req model.ReqQuestionSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	if req.CourseID == 0 {
		libs.Error(c, "参数错误")
		return
	}

	list, total, err := questionService.GetQuestionsByCondition(req)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	libs.Success(c, "查询成功", gin.H{
		"list":  list,
		"total": total,
	})
}
