package user

import (
	"encoding/json"
	"tms/model"
	"tmsc/libs"
	"tmsc/middleware"
	"tmsc/pkg/logger"
	"tmsc/services/auth"
	"tmsc/services/exam"
	eventSync "tmsc/services/sync"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

type ExamAPI struct{}

var examService = exam.NewExamService()
var examProgressService = exam.NewExamProgressService()

func init() {
	api := &ExamAPI{}

	v1 := middleware.NewGroup("v1", "API V1 版本")
	usersV1 := v1.Group("user", "客户端用户接口")
	examGroup := usersV1.Group("exam", "考试接口")
	{
		examGroup.Register("POST", "/grab", api.GrabExam, model.AuthUser, "同步考试列表")
		examGroup.Register("GET", "/list", api.GetExamList, model.AuthUser, "考试列表")
		examGroup.Register("GET", "/paper/:id", api.GetPaper, model.AuthUser, "试卷")
		examGroup.Register("POST", "/start", api.StartExam, model.AuthUser, "开始考试")
		examGroup.Register("POST", "/submit", api.SubmitAnswer, model.AuthUser, "提交考试答案")
		examGroup.Register("GET", "/detail/:id", api.GetExamStatus, model.AuthUser, "获取考试进度详情")
		examGroup.Register("GET", "/hisrory", api.GetExamHistory, model.AuthUser, "考试记录")
		examGroup.Register("GET", "/hisrory/detail/:id", api.GetExamHistoryDetail, model.AuthUser, "考试记录详情")
		examGroup.Register("GET", "/hisrory/answer/:id", api.GetAnswerHistoryDetail, model.AuthUser, "获取考试做题详情")
		examGroup.Register("GET", "/knowledge/analysis", api.GetKnowledgeAnalysis, model.AuthUser, "知识点分析")
	}
}

func (api *ExamAPI) GrabExam(c *gin.Context) {
	claims := auth.GetUserClaims(c)
	// 获取考试
	examsWithext, err := examService.GrabExams(claims.ID)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	var exams []model.Exams
	var examExts []model.ExamsExt
	for _, v := range examsWithext {
		exams = append(exams, v.Exams)
		examExts = append(examExts, v.Exts...)
	}

	// 获取试卷
	papers, err := examService.GrabPapers(claims.ID, exams...)
	if err != nil {
		logger.Logger.Error("GrabPapers 查询试卷失败", zap.Error(err))
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	// 获取试题
	paperQuestions, err := examService.GrabPaperQuestions(claims.ID, papers...)
	if err != nil {
		logger.Logger.Error("GrabPaperQuestions 查询试题失败", zap.Error(err))
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	questions, err := examService.GrabQuestions(claims.ID, paperQuestions...)
	if err != nil {
		logger.Logger.Error("GrabQuestions 查询试题失败", zap.Error(err))
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	questionExts, err := examService.GrabQuestionExts(claims.ID, questions...)
	if err != nil {
		logger.Logger.Error("GrabQuestionExts 查询试题失败", zap.Error(err))
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	// 同步数据
	if err := examService.UpsertData(claims.ID, exams, papers, paperQuestions, questions, examExts, questionExts); err != nil {
		libs.Error(c, "同步失败: "+err.Error())
		return
	}

	libs.Success(c, "同步成功", nil)
}

func (api *ExamAPI) GetExamList(c *gin.Context) {
	var req model.ReqExamSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	claims := auth.GetUserClaims(c)

	res, total, err := examService.GetExamList(claims.ID, req)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	libs.Success(c, "查询成功", gin.H{
		"list":  res,
		"total": total,
	})
}

func (api *ExamAPI) GetPaper(c *gin.Context) {
	paperID := c.Param("id")
	if paperID == "" {
		libs.Error(c, "无效的试卷ID")
		return
	}

	res, err := examService.GetPaperOfExam(cast.ToInt64(paperID))
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	// 隐藏题目答案
	for i := range res.PaperQuestions {
		var qcontent map[string]any
		err = json.Unmarshal([]byte(res.PaperQuestions[i].Content), &qcontent)
		if err != nil {
			logger.Logger.Error("JSON解析失败", zap.Error(err), zap.String("content", res.PaperQuestions[i].Content))
			libs.Error(c, "解析题目内容失败: "+err.Error())
			return
		}

		delete(qcontent, "answer")
		newContent, err := json.Marshal(qcontent)
		if err != nil {
			libs.Error(c, "序列化题目内容失败: "+err.Error())
			return
		}
		res.PaperQuestions[i].Content = string(newContent)
	}

	libs.Success(c, "查询成功", res)
}

// StartExam 开始考试
func (api *ExamAPI) StartExam(c *gin.Context) {
	claims := auth.GetUserClaims(c)
	var req struct {
		ExamID int64 `json:"id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if claims.ID == 0 || req.ExamID == 0 {
		libs.Error(c, "参数错误")
		return
	}

	id, err := examProgressService.CreateExamProgress(claims.ID, req.ExamID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "考试已创建", gin.H{"id": id})
}

func (api *ExamAPI) SubmitAnswer(c *gin.Context) {
	var req struct {
		ProgressID   int64              `json:"id"`
		Answers      []model.ExamAnswer `json:"answers"`
		SubmitStatue string             `json:"submit_status" binding:"required,oneof=commit terminate"` // commit / terminate
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if req.ProgressID == 0 {
		libs.Error(c, "无效的考试进度ID")
		return
	}

	progress, err := examProgressService.SubmitExam(req.ProgressID, req.Answers, req.SubmitStatue)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	go func(progress model.ExamProgress) {
		examProgressService.CheckAndFinishExam(progress.ExamID)
		claims := auth.GetUserClaims(c)
		userProgress, err := examProgressService.QueryExamProgressAndAnswers(req.ProgressID)
		if err != nil {
			logger.Logger.Error("查询失败", zap.Error(err))
			return
		}

		// 同步数据
		err = eventSync.NewEventSyncBuilder(claims.ID).
			Operator(claims.ID).
			SyncServer().
			EventType(eventSync.ExamSyncType).
			Data(userProgress).
			Save()
		if err != nil {
			logger.Logger.Error("插入同步记录失败", zap.Error(err))
			return
		}
	}(progress)

	libs.Success(c, "提交成功", gin.H{"progress_status": progress.Status})
}

func (api *ExamAPI) GetExamStatus(c *gin.Context) {
	progressID := cast.ToInt64(c.Param("id"))
	if progressID == 0 {
		libs.Error(c, "参数错误")
		return
	}

	progress, err := examProgressService.GetExamProgress(progressID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", progress)
}

func (api *ExamAPI) GetExamHistory(c *gin.Context) {
	claims := auth.GetUserClaims(c)
	res, err := examProgressService.GetExamHistory(claims.ID)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	libs.Success(c, "查询成功", res)
}

func (api *ExamAPI) GetExamHistoryDetail(c *gin.Context) {
	progressID := cast.ToInt64(c.Param("id"))
	if progressID == 0 {
		libs.Error(c, "参数错误")
		return
	}

	userProgress, err := examProgressService.QueryExamProgressAndAnswers(progressID)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	// 获取题目
	paper, err := examService.GetPaperOfExam(userProgress.ExamProgress.PaperID)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	for i := range paper.PaperQuestions {
		// 隐藏题目内容，避免数据过大
		paper.PaperQuestions[i].Content = ""
		paper.PaperQuestions[i].Answer = ""
	}

	libs.Success(c, "查询成功", gin.H{
		"exam":            userProgress.Exam,
		"paper":           paper.Papers,
		"paper_questions": paper.PaperQuestions,
		"progress":        userProgress.ExamProgress,
	})
}

// GetAnswerHistoryDetail 获取考试做题详情
func (api *ExamAPI) GetAnswerHistoryDetail(c *gin.Context) {
	progressID := cast.ToInt64(c.Param("id"))
	if progressID == 0 {
		libs.Error(c, "参数错误")
		return
	}

	questionID := cast.ToInt64(c.Query("question_id"))
	if questionID == 0 {
		libs.Error(c, "参数错误")
		return
	}

	answer, err := examProgressService.GetAnswerHistoryDetail(progressID, questionID)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	var questionContent map[string]any
	err = json.Unmarshal([]byte(answer.PaperQuestion.Content), &questionContent)
	if err != nil {
		libs.Error(c, "解析题目内容失败: "+err.Error())
		return
	}
	delete(questionContent, "answer")
	newContent, err := json.Marshal(questionContent)
	if err != nil {
		libs.Error(c, "序列化题目内容失败: "+err.Error())
		return
	}
	answer.PaperQuestion.Content = string(newContent)
	answer.PaperQuestion.Answer = ""

	libs.Success(c, "查询成功", answer)
}

func (api *ExamAPI) GetKnowledgeAnalysis(c *gin.Context) {
}
