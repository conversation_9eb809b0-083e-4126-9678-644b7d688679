package user

import (
	"tmsc/libs"
	"tmsc/middleware"

	"github.com/gin-gonic/gin"
)

type LearnHistoryAPI struct {
}

func init() {
	api := &LearnHistoryAPI{}

	v1 := middleware.NewGroup("v1", "API V1 版本")
	usersV1 := v1.Group("user", "客户端用户接口")
	learnHistoryV1 := usersV1.Group("learn_history", "学习历史接口")
	{
		learnHistoryV1.Register("GET", "/list", api.GetLearnHistory, "user", "学习历史列表")
	}
}

func (api *LearnHistoryAPI) GetLearnHistory(c *gin.Context) {
	// scorm 课件 与 虚拟课件的学习历史

	libs.Success(c, "查询成功", nil)
}
