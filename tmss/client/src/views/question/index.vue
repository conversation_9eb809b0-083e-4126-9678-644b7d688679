<template>

    <!-- 主内容区 -->
    <div class="flex-1 flex flex-col bg-gray-50 p-6 overflow-hidden">
        <!-- 顶部操作区 -->
        <div class="mb-6 flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <div>
                <el-button type="primary" class="!rounded-[8px] whitespace-nowrap" @click="createQuestion">
                    <el-icon class="mr-1">
                        <Plus />
                    </el-icon>
                    新增试题
                </el-button>
                <el-button type="success" class="!rounded-[8px] whitespace-nowrap"
                    @click="reviseListDrawerVisible = true">
                    <el-icon class="mr-1">
                        <View />
                    </el-icon>
                    修订列表
                </el-button>
                <el-button type="danger" class="!rounded-[8px] whitespace-nowrap">
                    <el-icon class="mr-1">
                        <Plus />
                    </el-icon>
                    批量导入
                </el-button>
            </div>


            <div class="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
                <el-select v-model="query.status" class="!w-48" placeholder="试题状态" clearable>
                    <el-option v-for="status in statusOptions" :key="status.value" :label="status.label"
                        :value="status.value" />
                </el-select>

                <el-input v-model=query.name class="!w-48" placeholder="搜索试题名称" clearable>
                    <template #append>
                        <el-button @click="getData">
                            <el-icon>
                                <Search />
                            </el-icon>
                        </el-button>
                    </template>
                </el-input>
                <el-button @click="resetSearch">
                    重置
                </el-button>
            </div>
        </div>

        <!-- 试题列表 -->
        <div class="flex-1 overflow-auto">
            <el-table :data="tableData" border style="width: 100%" v-loading="loading" empty-text="暂无数据">
                <el-table-column prop="question.title" label="试题名称" />
                <el-table-column prop="type" label="试题类型" align="center" width="100">
                    <template #default="{ row }">
                        <el-text :type="tagMap[row.question.question_type]" size="small"
                            class="!rounded-xl whitespace-nowrap">
                            {{ typeMap[row.question.question_type] }}
                        </el-text>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" align="center" width="100">
                    <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.question.status)" effect="light" class="!rounded-button">
                            {{ getStatusLabel(row.question.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="question.version" label="版本" width="80" />
                <el-table-column label="操作" align="center" width="280">
                    <template #default="{ row }">
                        <el-button
                            v-if="row.question.status == 'published' || row.question.status == 'reviewing' || row.question.status == 'deleted'"
                            size="small" @click="handleView(row.question)">
                            查看
                        </el-button>
                        <template v-if="row.question.status == 'draft' || row.question.status == 'rejected'">
                            <div class="flex justify-start">
                                <el-button size="small" type="primary" @click="handleEdit(row.question)">
                                    编辑
                                </el-button>
                                <el-button size="small" type="danger" @click="handleDelete(row.question)">
                                    删除
                                </el-button>
                                <!-- <el-button size="small" type="success" @click="handleSubmitReview(row)">
                                提交审核
                            </el-button> -->
                                <ApproveButton approve-code="questions" :data-id="row.question.id"
                                    :data-title="row.question.title" @success="getData" />
                            </div>
                        </template>
                    </template>
                </el-table-column>
                <!-- 修订 -->
                <el-table-column label="修订" width="190" align="center">
                    <template #default="{ row }">
                        <RevisionActions :approve-code="approveCode" :data="row.question" :rev="row.rev"
                            :show-create-button="row.question.status === 'published' && !row.rev"
                            @create="showReviseCreateDialog(row.question)" @edit="handleReviseEdit(row.rev)"
                            @delete="handleReviseDelete(row.rev)" @view="handleReviseView(row.rev)"
                            @refresh="getData" />
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 分页 -->
        <div class="shrink-0 flex justify-center mt-4">
            <el-pagination v-model:current-page="page" v-model:page-size="page_size" :total="total"
                layout=" prev, pager, next" @current-change="handleCurrentChange" />
        </div>
    </div>

    <!-- 新增/编辑试题弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800px">

        <el-form ref="formRef" :model="question" :rules="rules" label-width="100px" label-position="left"
            :disabled="actionType === 'view' || actionType === 'revise-view'">
            <el-form-item label="修订说明"
                v-if="actionType === 'revise-create' || actionType === 'revise-edit' || actionType === 'revise-view'">
                <el-input v-model="revNotes" type="textarea" :rows="2" placeholder="请输入修订说明" />
            </el-form-item>
            <el-form-item label="试题类型" prop="questions.question_type">
                <el-select v-model="question.questions.question_type" placeholder="请选择试题类型">
                    <el-option v-for="item in Object.keys(typeMap)" :key="item" :value="item" :label="typeMap[item]" />
                </el-select>
            </el-form-item>
            <el-form-item label="试题标题" prop="questions.title">
                <el-input v-model="question.questions.title" placeholder="请输入试题名称" />
            </el-form-item>

            <el-form-item label="试题内容" prop="questions.content">
                <el-input v-model="question.questions.content" type="textarea" :rows="4" placeholder="请输入试题内容" />
                <el-button v-if="question.questions.question_type === 'fill'" @click="insertPlaceholder" type="primary"
                    size="small" style="margin-top: 8px;">
                    插入空格标识符
                </el-button>
            </el-form-item>
            <el-form-item label="评分标准" prop="questions.score_points" v-if="showPoints">
                <el-input v-model="question.questions.score_points" type="textarea" placeholder="请输入评分标准" :rows="4" />
            </el-form-item>
            <el-form-item label="试题答案" prop="questions.answer">
                <template
                    v-if="question.questions.question_type === 'single' || question.questions.question_type === 'multiple'">
                    <div class="space-y-2 w-full">
                        <div v-for="(option, index) in question.questions.options" :key="index"
                            class="flex items-center">
                            <el-input v-model="option.content" :placeholder="`选项 ${index + 1}`" class="mr-2" />
                            <el-radio v-if="question.questions.question_type == 'single'"
                                v-model="question.questions.correctOptionIndex" :label="index">
                                {{ question.questions.correctOptionIndex === index ? '正确答案' : '' }}
                            </el-radio>
                            <el-checkbox v-else v-model="option.is_correct" />

                            <el-button v-if="index > 0 && actionType != 'view' && actionType != 'revise-view'"
                                type="danger" text @click="removeOption(index)">
                                <el-icon>
                                    <Close />
                                </el-icon>
                            </el-button>
                        </div>
                        <el-button v-if="actionType != 'view' && actionType != 'revise-view'" type="primary" text
                            @click="addOption" class="!rounded-button whitespace-nowrap">
                            <el-icon>
                                <Plus />
                            </el-icon>
                            添加选项
                        </el-button>
                    </div>
                </template>

                <template v-else-if="question.questions.question_type === 'judge'">
                    <el-radio-group v-model="question.questions.answer">
                        <el-radio value="false">错</el-radio>
                        <el-radio value="true">对</el-radio>
                    </el-radio-group>
                </template>

                <el-input v-else-if="question.questions.question_type === 'fill'" v-model="question.questions.answer"
                    type="textarea" :rows="2" placeholder="请输入填空答案，多个空用 | 分隔" />

                <el-input v-else v-model="question.questions.answer" type="textarea" :rows="4" placeholder="请输入答案" />
            </el-form-item>

            <el-form-item label="关联课件" prop="courseware_ids">
                <my-select v-model="question.courseware_ids" multiple placeholder="请选择关联课件" :func="getCoursewareList"
                    labelKey="courseware.title" valueKey="courseware.id" searchKey="name"
                    :initData="init_coursewares" />
            </el-form-item>
            <el-form-item label="参考分数" prop="questions.score">
                <el-input v-model="question.questions.score" type="number" placeholder="请输入参考分数" class="w-full" />
            </el-form-item>
            <el-form-item label="答题时间" prop="questions.minutes">
                <el-input v-model="question.questions.minutes" type="number" placeholder="请输入答题时间" class="w-full">
                    <template #append>分钟</template>
                </el-input>
            </el-form-item>
            <el-form-item label="关联章节" prop="chapter_ids">
                <el-tree-select :check-strictly="true" :default-expand-all="true" multiple
                    v-model="question.chapter_ids" :data="chapters" :props="chapter_props" />
            </el-form-item>
        </el-form>

        <template #footer v-if="actionType != 'view' && actionType != 'revise-view'">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitForm">保存</el-button>
        </template>
    </el-dialog>
    <!-- 修订列表 -->
    <el-drawer title="试题修订列表" v-model="reviseListDrawerVisible" size="90%" body-class="my-drawer">
        <RevisonList :approve-code="approveCode" />
    </el-drawer>
</template>

<script lang="ts" setup>

import { chapterList } from '@/api/chapter';
import { getCoursewareList } from '@/api/courseware';
import { createQuestionDraft, deleteQuestionDraft, editQuestionDraft, getQuestionDetail, getQuestionList, submitQuestionReview } from '@/api/question';
import { confirmMsg, errMsg, successMsg } from '@/utils/msg';
import { computed, onMounted, ref, watch } from 'vue';
import { typeMap, tagMap, getStatusTagType, getStatusLabel, statusOptions } from '@/api/status';
import { createRevision, editRevision, getRevisionDetail, deleteRevision } from '@/api/revision'
import { ElMessageBox } from 'element-plus';
const reviseListDrawerVisible = ref(false)
const approveCode = ref('questions');
const revNotes = ref(''); // 修订说明
const revId = ref(0); // 当前修订 ID
const dialogTitle = ref('创建试题')
const actionType = ref('create')
const query = ref({
    status: '',
    name: ''
})
const resetSearch = () => {
    query.value = {
        status: '',
        name: ''
    }
    getData()
}

// table
const tableData: any = ref([])
const loading = ref(false)
const page = ref(1)
const page_size = ref(10)
const total = ref(0)
const handleCurrentChange = (val: any) => {
    page.value = val
    getData()
}
const getData = async () => {
    loading.value = true
    const params = {
        page: page.value,
        ...(query.value.name && { name: query.value.name }),
        ...(query.value.status && { status: query.value.status })
    }
    const res = await getQuestionList(params)
    loading.value = false
    tableData.value = res.list || []
    total.value = res.total || 0
}
const init_coursewares = ref([])
const setQuestion = (res: any) => {
    //console.log(res)
    const coursewares = res.coursewares || []
    const chapters = res.chapters || []


    init_coursewares.value = coursewares || []
    return {
        questions: {
            ...res.question,
            answer: res.question.answer ? JSON.parse(res.question.answer) : '',
            options: res.question.options ? JSON.parse(res.question.options) : [],
            correctOptionIndex: res.question.question_type == 'single' ? JSON.parse(res.question.options).findIndex((item: any) => item.is_correct) : null

        },
        courseware_ids: coursewares.map((item: any) => item.id),
        chapter_ids: chapters.map((item: any) => item.id),
    }
}
const handleView = async (row: any) => {
    dialogTitle.value = '查看题目'
    actionType.value = 'view'
    const res = await getQuestionDetail(row.id)
    question.value = setQuestion(res)
    dialogVisible.value = true
}

const handleEdit = async (row: any) => {
    dialogTitle.value = '编辑题目'
    actionType.value = 'edit'
    const res = await getQuestionDetail(row.id)
    question.value = setQuestion(res)
    dialogVisible.value = true
}
const handleDelete = (row: any) => {
    confirmMsg('确定要删除吗？', '提示', async (action) => {
        if (action) {
            const res = await deleteQuestionDraft(row.id)
            successMsg(res.message)
            getData()
        }
    })
}
const createQuestion = () => {
    dialogVisible.value = true
    actionType.value = 'create'
    dialogTitle.value = '创建题目';
    question.value = initQuestion()
    formRef.value?.resetFields()
}
const initQuestion = () => {
    return {
        questions: {
            title: '',
            question_type: '', // 单选题（single）、多选题（multiple）、判断题（judge）、填空题（fill）、简答题（short）
            content: '',
            answer: '',
            score_points: '', // 新增评分标准字段
            score: '',
            minutes: '',
            options: [],
            correctOptionIndex: null
        },
        courseware_ids: [],
        chapter_ids: [],
    }
}

const question: any = ref(initQuestion())

const showPoints = computed(() => {
    const type = question.value.questions.question_type
    return type == 'fill' || type === 'short' || type === 'discuss' || type === 'analyze' || type === 'comprehensive' || type === 'self'
})
const dialogVisible = ref(false)

const formRef = ref()


// watch(dialogVisible, (val) => {
//     if (!val) {
//         question.value = initQuestion()
//         formRef.value?.resetFields()
//     }
// })
watch(() => question.value.questions.question_type, () => {
    formRef.value?.clearValidate()
})



const addOption = () => {
    question.value.questions.options.push({
        id: question.value.questions.options.length + 1,
        content: '',
        is_correct: false
    })
}
const removeOption = (index: number) => {
    question.value.questions.options.splice(index, 1)
}
const validateAnswer = (rule: any, value: any, callback: any) => {
    const type = question.value.questions.question_type

    if (type === 'single') {
        // 单选题：检查是否选择了正确答案
        if (question.value.questions.correctOptionIndex === null) {
            callback(new Error('单选题必须有一个正确答案'))
        } else {
            callback()
        }
    } else if (type === 'multiple') {
        // 多选题：检查是否至少有两个正确答案
        const correctOptions = question.value.questions.options.filter((opt: any) => opt.is_correct)
        if (correctOptions.length < 1) {
            callback(new Error('请至少选择一个正确答案'))
        } else {
            callback()
        }
    } else if (type === 'judge') {
        const answer = value.toString()
        if (!answer) {
            callback(new Error('请选择正确的答案'))
        } else {
            callback()
        }
    } else if (type === 'fill') {
        // 填空题：根据 {content} 个数判断答案是否包含 |
        const content = question.value.questions.content
        const blankCount = (content.match(/{content}/g) || []).length

        if (blankCount === 0) {
            callback(new Error('题目内容中未找到空格标识符 {content}'))
            return
        }

        if (blankCount > 1 && !value.includes('|')) {
            callback(new Error(`题目中有 ${blankCount} 个空格，请用 | 分隔多个答案`))
            return
        }

        const answerList = value.split('|').map((a: string) => a.trim())
        if (blankCount !== answerList.length) {
            callback(new Error(`空格数量为 ${blankCount}，答案数量为 ${answerList.length}，请保持一致`))
            return
        }

        callback()
    } else {
        // 其他题型：检查 answer 是否为空
        if (!value || value.trim() === '') {
            callback(new Error('请输入答案'))
        } else {
            callback()
        }
    }
}
const rules = {
    questions: {
        title: [
            { required: true, message: '请输入试题名称', trigger: 'blur' }
        ],
        question_type: [
            { required: true, message: '请选择试题类型', trigger: 'change' }
        ],
        content: [
            { required: true, message: '请输入试题内容', trigger: 'blur' }
        ],
        score_points: [
            { required: true, message: '请输入评分标准', trigger: 'blur' }
        ],
        answer: [
            { validator: validateAnswer, trigger: ['blur', 'change'] }
        ],
        score: [
            { required: true, message: '请输入分值', trigger: 'blur' }
        ],
        minutes: [
            { required: true, message: '请输入答题时间（分钟）', trigger: 'blur' }
        ]

    },
    courseware_ids: [
        { required: true, message: '请选择关联课件', trigger: 'change' }
    ],
    chapter_ids: [
        { required: true, message: '请选择关联章节', trigger: 'change' }
    ]

}
const getDataQuestion = () => {
    let questions
    const type = question.value.questions.question_type
    if (type == 'single') {
        question.value.questions.options.forEach((o: any, index: number) => {
            o.is_correct = index == question.value.questions.correctOptionIndex
        })
        questions = {
            ...question.value.questions,
            options: JSON.stringify(question.value.questions.options),
            answer: JSON.stringify(question.value.questions.options[question.value.questions.correctOptionIndex].id)
        }
    } else if (type == 'multiple') {
        questions = {
            ...question.value.questions,
            options: JSON.stringify(question.value.questions.options),
            answer: JSON.stringify(question.value.questions.options.filter((o: any) => o.is_correct).map((o: any) => o.id))
        }
    } else {
        const { options, ...target } = question.value.questions
        questions = {
            ...target,
            answer: JSON.stringify(question.value.questions.answer)
        }
    }
    const data = { ...question.value, questions }
    data.questions.score = data.questions.score * 1
    data.questions.minutes = data.questions.minutes * 1
    return data
}
const submitForm = () => {
    formRef.value.validate(async (valid: boolean) => {
        if (valid) {
            const data = getDataQuestion()
            console.log(data, '----')
            //const res = isEdit.value ? await editQuestionDraft(data) : await createQuestionDraft(data)
            let res: any
            if (actionType.value == 'create') {
                res = await createQuestionDraft(data)
            }
            else if (actionType.value == 'edit') {
                res = await editQuestionDraft(data)
            }
            else if (actionType.value == 'revise-create' || actionType.value == 'revise-edit') {
                if (!revNotes.value) return errMsg('请输入修订说明')
                const changes = {
                    formData: data,
                    coursewares: init_coursewares.value,
                }
                const saveData: any = {
                    module_key: approveCode.value,
                    original_id: data.questions.id,
                    notes: revNotes.value,
                    changes: JSON.stringify(changes),
                }
                if (actionType.value == 'revise-edit') {
                    saveData.id = revId.value
                    res = await editRevision(saveData)
                } else {
                    console.log('createRevision', saveData)
                    res = await createRevision(saveData)
                }

            }
            successMsg(res.message)
            dialogVisible.value = false
            getData()
        } else {
            return false
        }
    })
}

const chapters = ref([])
const getChapters = async () => {
    const res = await chapterList()
    chapters.value = res.data || []
}
const chapter_props = {
    label: 'name',
    value: 'id'
}
const insertPlaceholder = () => {
    const placeholder = `{content}`;
    question.value.questions.content += placeholder;
};
// 初始化修订表单数据
const initReviseData = async (row: any) => {
    //console.log(row, 'row')
    revId.value = row.id;
    revNotes.value = row.notes;
    const changes = JSON.parse(row.changes);
    //console.log(changes, 'changes')
    init_coursewares.value = changes.coursewares || []
    let q = changes.formData.questions
    q = {
        ...q,
        answer: q.answer ? JSON.parse(q.answer) : '',
        options: q.options ? JSON.parse(q.options) : [],
        correctOptionIndex: q.question_type == 'single' ? JSON.parse(q.options).findIndex((item: any) => item.is_correct) : null
    }
    question.value = { ...changes.formData, questions: q }


};

// 创建修订
const showReviseCreateDialog = async (row: any) => {
    dialogTitle.value = '创建修订';
    actionType.value = 'revise-create';
    revNotes.value = '';
    revId.value = 0;

    const res = await getQuestionDetail(row.id)
    question.value = setQuestion(res)
    dialogVisible.value = true;
};

// 编辑修订
const handleReviseEdit = async (row: any) => {
    dialogTitle.value = '编辑修订';
    actionType.value = 'revise-edit';
    await initReviseData(row);
    dialogVisible.value = true;
};

// 查看修订
const handleReviseView = async (row: any) => {
    dialogTitle.value = '查看修订';
    actionType.value = 'revise-view';
    await initReviseData(row);
    dialogVisible.value = true;
};

// 删除修订
const handleReviseDelete = async (row: any) => {
    ElMessageBox.confirm('确定要删除修订吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
            const res = await deleteRevision(row.id);
            if (res.success) {
                successMsg('删除成功');
                getData();
            } else {
                errMsg(res.message || '删除失败');
            }
        })
        .catch(() => { });
};
onMounted(() => {
    getData()
    getChapters()
})
</script>

<style scoped>
.el-table :deep(.el-table__cell) {
    padding: 12px 0;
}

.el-tag {
    margin-right: 4px;
}

.el-form-item {
    margin-bottom: 22px;
}
</style>
