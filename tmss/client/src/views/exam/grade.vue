<template>
    <div class="flex-1 flex flex-col bg-gray-50 p-6 overflow-auto">
        <div class="flex justify-between items-center border-b border-gray-200 pb-6">
            <!-- <div class="space-x-4 flex items-center">

            </div> -->
            <!-- <div class="flex space-x-3">
                <el-button type="success" >
                    <el-icon class="mr-1">
                        <Check />
                    </el-icon>
                    自动批改
                </el-button>
                <el-button type="primary">
                    <el-icon class="mr-1">
                        <Timer />
                    </el-icon>
                    批量发布成绩
                </el-button>
            </div> -->
        </div>

        <div class="flex-1 overflow-auto ">
            <el-table :data="tableData" border v-loading="loading">
                <el-table-column v-for="col in cols" :key="col.prop" :label="col.label" :prop="col.prop"
                    show-overflow-tooltip align="center" />
                <el-table-column show-overflow-tooltip label="开始时间" prop="start_at" align="center">
                    <template #default="{ row: { start_at } }">
                        <el-text type="primary">
                            {{ formatTime(start_at) }}
                        </el-text>
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip label="结束时间" prop="end_at" align="center">
                    <template #default="{ row: { end_at } }">
                        <el-text type="danger">
                            {{ formatTime(end_at) }}
                        </el-text>
                    </template>
                </el-table-column>

                <el-table-column label="操作" width="200">
                    <template #default="{ row }">
                        <div class="space-x-2">
                            <el-button type="primary" link @click="assignTeacher(row.id)">
                                分配老师
                            </el-button>
                            <el-button type="success" link @click="gradingExam(row.id)">
                                批改
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>


        <div class="flex justify-center mt-4">
            <el-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total"
                layout="prev, pager, next" @current-change="handleCurrentChange" />
        </div>

        <!-- 学生列表 -->
        <el-dialog v-model="showStudentList" title="学生列表" width="800px">
            <div v-if="showStudentList">
                <el-button class="mb-4" type="primary" @click="showTeacherDialog = true"
                    :disabled="!progress_ids.length">批量分配</el-button>
                <el-button class="mb-4" :disabled="!progress_ids.length" @click="changeTeacher">批量修改</el-button>
                <el-table :data="students" border v-loading="student_loading" row-key="id"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column prop="student_name" label="姓名" align="center" />
                    <el-table-column prop="teacher_name" label="阅卷老师" align="center" />

                    <!-- <el-table-column label="操作" align="center">
                        <template #default="{ row }">
                            <el-button :disabled="row.reviewer_id ? false : true" type="danger" link
                                @click="changeTeacher(row)">更改老师</el-button>
                        </template>
                    </el-table-column> -->
                </el-table>
                <div class="flex mt-4 justify-center">
                    <el-pagination v-model:current-page="student_page" v-model:page-size="student_page_size"
                        :total="student_total" layout=" prev, pager, next" @click="handleStudentPageChange" />
                </div>
            </div>
            <!-- 老师dialog -->
            <el-dialog v-model="showTeacherDialog" title="分配老师" :show-close="false">
                <el-select v-model="teacher_id" placeholder="请选择老师" class="w-full">
                    <el-option v-for="item in teachers" :key="item.id" :label="item.username"
                        :value="item.id"></el-option>
                </el-select>
                <template #footer>
                    <el-button @click="showTeacherDialog = false">取消</el-button>
                    <el-button type="primary" @click="handleAssignTeacher">确定</el-button>
                </template>
            </el-dialog>
        </el-dialog>

        <!-- 老师阅卷列表 -->
        <el-drawer v-model="drawerVisible" size="90%" title="老师阅卷列表">
            <grade-list v-if="drawerVisible" :id="exam_id" />
        </el-drawer>
    </div>
</template>

<script lang="ts" setup>
import { getExamUserList } from '@/api/exam';
import { batchReview, getExamList, getExamStudents, updateReview } from '@/api/score';
import { successMsg } from '@/utils/msg';
import { onMounted, ref, watch } from 'vue';
// constant
const typeMap: any = {
    'regular': '平时考试',
    'final': '结业考试',
    'retake': '补考'
}
//  page
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleCurrentChange = async (val: number) => {
    page.value = val
    getData()
}
// table
const cols = [
    { prop: 'name', label: '考试名称' },
    { prop: 'exam_type', label: '考试类型' },
    { prop: 'total_minutes', label: '考试时长（分钟）' }
]
const loading = ref(false)
const tableData: any = ref([])
const getData = async () => {
    loading.value = true

    const params = {
        page: page.value
    }
    const res = await getExamList(params)
    tableData.value = res.list || []
    tableData.value = tableData.value.map((item: any) => {
        item.exam_type = typeMap[item.exam_type]
        return item
    })
    total.value = res.total || 0
    loading.value = false
}

const formatTime = (time: any) => {
    const date = new Date(time * 1000)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}
// dialog
const showStudentList = ref(false)

const exam_id = ref(0)
watch(exam_id, (val) => {
    if (val && showStudentList.value) {
        getStudents()
        getTeachers()
        progress_ids.value = []
    } else {
        student_page.value = 1
    }
})
// 分配老师
const assignTeacher = (id: number) => {
    exam_id.value = id
    showStudentList.value = true
}

// students
const student_loading = ref(false)
const students = ref([])
const student_page = ref(1)
const student_page_size = ref(10)
const student_total = ref(0)
const selectable = (row: any) => {
    return !row.reviewer_id
}

const handleStudentPageChange = async (val: number) => {
    student_page.value = val
    getStudents()
}

const getStudents = async () => {
    student_loading.value = true

    const params = {
        page: student_page.value,
        exam_id: exam_id.value
    }
    const res = await getExamStudents(params)
    students.value = res.list || []
    student_total.value = res.total || 0
    student_loading.value = false
}

const progress_ids = ref([])
const handleSelectionChange = (val: any) => {
    progress_ids.value = val.map((item: any) => item.id)
}


// teachers
const teachers: any = ref([])
const teacher_id = ref('')
const showTeacherDialog = ref(false)
watch(showTeacherDialog, (val) => {
    if (!val) {
        teacher_id.value = ''
        is_change.value = false
    }
})
const getTeachers = async () => {
    const params = {
        ext_key: 'teacher',
        exam_id: exam_id.value
    }
    const res = await getExamUserList(params)
    teachers.value = res.list || []
}
const handleAssignTeacher = async () => {
    const data = is_change.value ? {
        teacher_id: teacher_id.value,
        progress_ids: progress_ids.value,
    } : {
        exam_id: exam_id.value,
        teacher_id: teacher_id.value,
        progress_ids: progress_ids.value,
    }
    const res = is_change.value ? await updateReview(data) : await batchReview(data)
    successMsg(res.message)
    showTeacherDialog.value = false
    getStudents()
}

const is_change = ref(false)
const changeTeacher = () => {
    showTeacherDialog.value = true
    is_change.value = true
}

// drawer
const drawerVisible = ref(false)
const gradingExam = (id: number) => {
    exam_id.value = id
    drawerVisible.value = true
}
// hooks
onMounted(() => {
    getData()
})
</script>

<style scoped>
:deep(.el-drawer__header) {
    margin-bottom: 0 !important;
}

:deep(.el-drawer__body) {
    display: flex;
    overflow: hidden;
}
</style>
