<template>
    <div class="flex-1 bg-gray-50 flex flex-col">

        <!-- 试卷列表 -->
        <div class=" px-4 py-6 mt-4 flex-1 overflow-auto">
            <el-table :data="tableData" style="width: 100%" border v-loading="loading">
                <el-table-column label="得分">
                    <template #default="{ row }">
                        <el-text type="danger">{{ `${row.score}/${row.total_score}` }}</el-text>
                    </template>
                </el-table-column>
                <el-table-column label="状态">
                    <template #default="{ row }">
                        <el-tag v-if="row.status == 'reviewed'" type="success">已阅卷</el-tag>
                        <el-tag v-else type="warning">未阅卷</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="阅卷时间">
                    <template #default="{ row }">
                        {{ row.reviewed_at ? 'time' : '' }}
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template #default="{ row }">
                        <el-button type="warning" plain>开始阅卷</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 分页 -->
        <div class="mt-4 flex justify-center items-center">
            <el-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total"
                layout="prev, pager, next" background @current-change="handleCurrentChange"></el-pagination>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { getTaskList } from '@/api/score';
import { ref, computed, onMounted } from 'vue';
// props
const props = defineProps<{ id: number }>()
// page
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleCurrentChange = (val: number) => {
    page.value = val
    getData()
}
// table
const tableData = ref([])
const loading = ref(false)
const getData = async () => {
    loading.value = true
    const params = {
        page: page.value,
        exam_id: props.id,
    }
    const res = await getTaskList(params)
    console.log(res, 'res')
    tableData.value = res.list || []
    total.value = res.total || 0
    loading.value = false
}

onMounted(() => {
    getData()
});
</script>

<style scoped>
.el-table {
    margin-top: 16px;
}

.el-pagination {
    margin-top: 16px;
}

.el-tag {
    margin-right: 4px;
}
</style>
