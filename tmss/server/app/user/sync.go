package user

import (
	"encoding/json"
	"fmt"
	"log"
	"time"
	"tms/libs"
	"tms/middleware"
	"tms/model"
	"tms/pkg/db"
	"tms/services/sync"
	"tms/utils"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

func init() {
	UserV1 := GetVersion()
	syncV1 := UserV1.Group("sync", "认证接口")
	syncV1.Use(middleware.SignAuthMiddleware())
	{
		syncV1.Register("GET", "/get", GetSyncData, "user", "获取同步数据")
		syncV1.Register("POST", "/post", SyncData, "user", "提交同步数据")
		syncV1.Register("POST", "/query", RpcQuery, "user", "RPC查询")
	}
}
func GetSyncData(c *gin.Context) {
	claims, err := utils.GetClaims(c)
	if err != nil {
		libs.Error(c, "获取用户信息失败")
		return
	}
	userId := claims.ID
	res, err := sync.GetUserUnsyncedRecord(userId)
	if err != nil {
		libs.Error(c, "获取同步数据失败")
		return
	}
	libs.Success(c, "获取同步数据成功", res)
}
func SyncData(c *gin.Context) {
	claims, err := utils.GetClaims(c)
	if err != nil {
		libs.Error(c, "获取用户信息失败")
		return
	}
	userId := claims.ID
	request := model.SyncRecords{}
	if err := c.ShouldBindJSON(&request); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	err = sync.HandleWriteSync(request.SyncType, request.SyncObject, request.SyncData, userId, request.EventID)
	if err != nil {
		log.Printf("Failed to write sync data: %v", err)
		libs.Error(c, "同步数据失败")
		return
	}
	libs.Success(c, "同步数据成功", nil)
}

func RpcQuery(c *gin.Context) {
	var req struct {
		Method string         `json:"method"`
		Params map[string]any `json:"params"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误: "+err.Error())
		return
	}

	log.Printf("RPC Query - Method: %s, Params: %+v", req.Method, req.Params)

	var result []byte
	var err error

	switch req.Method {
	case "ListTeachPlans":
		userID := cast.ToInt64(req.Params["user_id"])
		if userID == 0 {
			libs.Error(c, "无效的用户ID")
		}

		var userRef model.StudentMap
		if err = db.DB.Where("user_id = ?", userID).First(&userRef).Error; err != nil {
			log.Printf("获取用户关联信息失败: %v", err)
			libs.Error(c, "获取用户关联信息失败")
			return
		}

		var planIDS []int64
		if err = db.DB.Model(&model.TeachingPlanExt{}).Where("ext_key = ? AND ext_value = ?", "class_id", cast.ToString(userRef.ClassID)).Pluck("plan_id", &planIDS).Error; err != nil {
			log.Printf("获取计划ID列表失败: %v", err)
			libs.Error(c, "获取计划ID列表失败")
			return
		}

		plans := make([]model.TeachingPlan, 0)
		now := time.Now().Unix()
		if err = db.DB.
			Where("id IN (?) AND status = ? AND start_at <= ? AND end_at >= ?", planIDS, model.TeachingPlanStatusPublished, now, now).
			Order("id DESC").
			Find(&plans).Error; err != nil {
			log.Printf("获取计划列表失败: %v", err)
			libs.Error(c, "获取计划列表失败")
			return
		}

		planWithExt := make([]model.TeachPlanWithExts, 0)
		var exts []model.TeachingPlanExt
		if err := db.DB.Where("ext_key = ? AND ext_value = ?", "class_id", userRef.ClassID).Find(&exts).Error; err != nil {
			log.Printf("获取计划扩展信息失败: %v", err)
			libs.Error(c, "获取计划扩展信息失败")
			return
		}
		extsMap := make(map[int64][]model.TeachingPlanExt)
		for _, ext := range exts {
			extsMap[ext.PlanID] = append(extsMap[ext.PlanID], ext)
		}

		for _, plan := range plans {
			planWithExt = append(planWithExt, model.TeachPlanWithExts{
				Plan: plan,
				Exts: extsMap[plan.ID],
			})
		}

		result, err = json.Marshal(&planWithExt)

	case "ListCourse":
		// 示例：获取课程列表
		planIDS := cast.ToInt64Slice(req.Params["plan_ids"])
		if len(planIDS) == 0 {
			libs.Error(c, "无效的计划ID列表")
			return
		}

		courses := make([]model.Courses, 0)
		if err = db.DB.
			Where("plan_id in (?) AND status = ?", planIDS, model.TeachingPlanStatusPublished).
			Order("id desc").Find(&courses).Error; err != nil {
			log.Printf("获取课程列表失败: %v", err)
			libs.Error(c, "获取课程列表失败")
			return
		}

		result, err = json.Marshal(&courses)

	case "ListResourceCategory":
		// 示例：获取资源分类列表
		resourceCategories := make([]model.ResourceCategory, 0)
		db.DB.Find(&resourceCategories)
		result, err = json.Marshal(&resourceCategories)

	case "ListResource":
		// 示例：获取资源列表
		resources := make([]model.Resources, 0)
		db.DB.Where("status = ?", model.ResourceStatusPublished).Find(&resources)
		result, err = json.Marshal(&resources)

	case "ListChapter":
		courseIDS := cast.ToInt64Slice(req.Params["course_ids"])
		if len(courseIDS) == 0 {
			libs.Error(c, "无效的课程ID列表")
			return
		}
		var courses []model.Courses
		if err = db.DB.Where("id in (?)", courseIDS).Find(&courses).Error; err != nil {
			log.Printf("获取课程列表失败: %v", err)
			libs.Error(c, "获取课程列表失败")
			return
		}
		chapterIDS := make([]int64, 0)
		for _, course := range courses {
			chapterIDS = append(chapterIDS, course.ChapterID)
		}

		// 收集所有需要查询的章节ID
		finalChapterIDMap := make(map[int64]struct{})
		queue := make([]int64, 0)

		// 初始化队列，将所有初始章节ID加入队列
		for _, initialID := range chapterIDS {
			if _, ok := finalChapterIDMap[initialID]; !ok {
				finalChapterIDMap[initialID] = struct{}{}
				queue = append(queue, initialID)
			}
		}

		// BFS 遍历：同时向上和向下查找所有相关章节
		for len(queue) > 0 {
			currentID := queue[0]
			queue = queue[1:]

			// 查找父节点
			var parentChapter model.Chapter
			if err := db.DB.Where("id = ?", currentID).First(&parentChapter).Error; err != nil {
				log.Printf("获取章节失败: %v", err)
				continue
			}
			if parentChapter.ParentID != 0 {
				if _, ok := finalChapterIDMap[parentChapter.ParentID]; !ok {
					finalChapterIDMap[parentChapter.ParentID] = struct{}{}
					queue = append(queue, parentChapter.ParentID)
				}
			}

			// 查找子节点
			var children []model.Chapter
			if err := db.DB.Where("parent_id = ?", currentID).Find(&children).Error; err != nil {
				log.Printf("获取章节子节点失败: %v", err)
				continue
			}
			for _, child := range children {
				if _, ok := finalChapterIDMap[child.ID]; !ok {
					finalChapterIDMap[child.ID] = struct{}{}
					queue = append(queue, child.ID)
				}
			}
		}

		// 将 map 的 key 转换为 slice
		var idsToQuery []int64
		for id := range finalChapterIDMap {
			idsToQuery = append(idsToQuery, id)
		}

		// 根据收集到的所有章节ID查询章节
		chapters := make([]model.Chapter, 0)
		if len(idsToQuery) > 0 {
			if err = db.DB.Where("id in (?)", idsToQuery).Find(&chapters).Error; err != nil {
				log.Printf("获取章节列表失败: %v", err)
				libs.Error(c, "获取章节列表失败")
				return
			}
		}

		result, err = json.Marshal(&chapters)

	case "ListCourseware":
		chapterIDS := cast.ToInt64Slice(req.Params["chapter_ids"])
		if len(chapterIDS) == 0 {
			libs.Error(c, "无效的章节ID列表")
			return
		}
		coursewares := make([]model.Courseware, 0)
		if err = db.DB.Where("chapter_id in (?) and status = ?", chapterIDS, model.CoursewareStatusPublished).Find(&coursewares).Error; err != nil {
			log.Printf("获取课件列表失败: %v", err)
			libs.Error(c, "获取课件列表失败")
			return
		}
		coursewareWithext := make([]model.CoursewareWithExt, 0)
		for _, v := range coursewares {
			exts := make([]model.CoursewareExt, 0)
			if err = db.DB.Where("courseware_id = ?", v.ID).Find(&exts).Error; err != nil {
				log.Printf("获取课件扩展列表失败: %v", err)
				libs.Error(c, "获取课件扩展列表失败")
				return
			}
			coursewareWithext = append(coursewareWithext, model.CoursewareWithExt{
				Courseware: v,
				Exts:       exts,
			})
		}

		result, err = json.Marshal(&coursewareWithext)

	case "ListQuestionsByConds":
		chapterIDS := make([]int64, 0)
		chapterIDS = cast.ToInt64Slice(req.Params["chapter_ids"])
		courseIDS := cast.ToInt64Slice(req.Params["course_ids"])
		if len(courseIDS) > 0 {
			for _, id := range courseIDS {
				ids, err := coursesService.GetAttachedChapterIDS(id)
				if err != nil {
					libs.Error(c, "获取章节ID列表失败")
					return
				}
				chapterIDS = append(chapterIDS, ids...)
			}
		}

		if len(chapterIDS) == 0 {
			libs.Error(c, "无效的章节ID列表")
			return
		}
		coursewareIDS := make([]int64, 0)

		var questionIDS []int64
		// 查询该章节下所有关联的试题ID（来自 questions_ext 表）
		if len(chapterIDS) > 0 {
			queryExt := db.DB.Model(&model.QuestionsExt{}).
				Where("ext_key = ? AND ext_value in (?)", "chapter", chapterIDS).
				Distinct("question_id").
				Pluck("question_id", &questionIDS)

			coursewareIDS = cast.ToInt64Slice(req.Params["courseware_ids"])
			if len(coursewareIDS) > 0 {
				queryExt = queryExt.Where("ext_key = ? AND ext_value in (?)", "courseware", coursewareIDS)
			}

			if err := queryExt.Error; err != nil {
				libs.Error(c, "获取试题ID列表失败")
				return
			}
		}

		questions := make([]model.Questions, 0)
		if err = db.DB.Where("id in (?) and status = ?", questionIDS, model.QuestionStatusPublished).Find(&questions).Error; err != nil {
			log.Printf("获取试题列表失败: %v", err)
			libs.Error(c, "获取试题列表失败")
			return
		}
		result, err = json.Marshal(&questions)
	case "ListExam":
		exams := make([]model.Exams, 0)
		if err = db.DB.Where("status = ?", model.ExamStatusPublished).Find(&exams).Error; err != nil {
			log.Printf("获取考试列表失败: %v", err)
			libs.Error(c, "获取考试列表失败")
			return
		}
		examsWithext := make([]model.ExamsWithExt, 0)
		for _, v := range exams {
			exts := make([]model.ExamsExt, 0)
			if err = db.DB.Where("exam_id = ?", v.ID).Find(&exts).Error; err != nil {
				log.Printf("获取考试扩展列表失败: %v", err)
				libs.Error(c, "获取考试扩展列表失败")
				return
			}
			examsWithext = append(examsWithext, model.ExamsWithExt{
				Exams: v,
				Exts:  exts,
			})
		}
		result, err = json.Marshal(&examsWithext)

	case "ListPaper":
		paperIDS := cast.ToInt64Slice(req.Params["paper_ids"])
		if len(paperIDS) == 0 {
			log.Printf("ListPaper 无效的试卷ID列表, params: %+v", req.Params)
			libs.Error(c, "无效的试卷ID列表")
			return
		}
		papers := make([]model.Papers, 0)
		if err = db.DB.Where("id in (?) and status = ?", paperIDS, model.PaperStatusPublished).Find(&papers).Error; err != nil {
			log.Printf("获取试卷列表失败: %v", err)
			libs.Error(c, "获取试卷列表失败")
			return
		}
		// TODO: 试卷里的参考课件 应该来自当前教学计划下的课件
		result, err = json.Marshal(&papers)
	case "ListPaperQuestion":
		paperIDS := cast.ToInt64Slice(req.Params["paper_ids"])
		if len(paperIDS) == 0 {
			log.Printf("ListPaperQuestion 无效的试卷ID列表, params: %+v", req.Params)
			libs.Error(c, "无效的试卷ID列表")
			return
		}
		paperQuestions := make([]model.PaperQuestions, 0)
		if err := db.DB.Where("paper_id IN (?)", paperIDS).Find(&paperQuestions).Error; err != nil {
			log.Printf("获取试卷题目失败: %v", err)
			libs.Error(c, "获取试卷题目失败")
			return
		}
		result, err = json.Marshal(&paperQuestions)
	case "ListQuestionOfPaper":
		ids := cast.ToInt64Slice(req.Params["question_ids"])
		if len(ids) == 0 {
			log.Printf("ListQuestionOfPaper 无效的试题ID列表, params: %+v", req.Params)
			libs.Error(c, "无效的试题ID列表")
			return
		}
		questions := make([]model.Questions, 0)
		if err = db.DB.Where("id in (?) and status = ?", ids, model.QuestionStatusPublished).Find(&questions).Error; err != nil {
			log.Printf("获取试题列表失败: %v", err)
			libs.Error(c, "获取试题列表失败")
			return
		}
		result, err = json.Marshal(&questions)
	case "ListQuestionExts":
		ids := cast.ToInt64Slice(req.Params["question_ids"])
		if len(ids) == 0 {
			log.Printf("ListQuestionExts 无效的试题ID列表, params: %+v", req.Params)
			libs.Error(c, "无效的试题ID列表")
			return
		}
		baseQuery := db.DB.Where("question_id in (?)", ids)

		chapterIDS := cast.ToInt64Slice(req.Params["chapter_ids"])
		if len(chapterIDS) > 0 {
			baseQuery = baseQuery.Where("ext_key = ? AND ext_value in (?)", "chapter", chapterIDS)
		}

		exts := make([]model.QuestionsExt, 0)
		if err = baseQuery.Find(&exts).Error; err != nil {
			log.Printf("获取试题扩展列表失败: %v", err)
			libs.Error(c, "获取试题扩展列表失败")
			return
		}
		result, err = json.Marshal(&exts)

	default:
		err = fmt.Errorf("未找到方法: %s", req.Method)
		return
	}

	if err != nil {
		libs.Error(c, fmt.Sprintf("查询失败: %v", err))
		return
	}

	libs.Success(c, "查询成功", result)
}
