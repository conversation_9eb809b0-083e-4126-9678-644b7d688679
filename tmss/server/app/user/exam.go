// app/user/exam.go
package user

import (
	"encoding/json"
	"log/slog"
	"tms/libs"
	"tms/model"
	"tms/services/auth"
	"tms/services/exam"
	eventSync "tms/services/sync"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ExamAPI struct{}

var examProgressService = exam.NewExamProgressService()
var examService = exam.NewExamService()
var paperService = exam.NewPaperService()

func init() {
	api := &ExamAPI{}
	usersV1 := GetVersion()
	examGroup := usersV1.Group("exam", "考试接口")
	{
		examGroup.Register("GET", "/list", api.GetExamList, model.AuthUser, "获取考试列表")
		examGroup.Register("GET", "/paper/:id", api.GetPaper, model.AuthUser, "试卷")
		examGroup.Register("POST", "/start", api.StartExam, model.AuthUser, "开始考试")
		examGroup.Register("POST", "/submit", api.SubmitExam, model.AuthUser, "提交考试答案")
		examGroup.Register("GET", "/detail/:id", api.GetExamDetail, model.AuthUser, "获取考试进度详情")
		examGroup.Register("GET", "/hisrory", api.GetExamHistory, model.AuthUser, "考试记录")
		examGroup.Register("GET", "/hisrory/detail/:id", api.GetExamHistoryDetail, model.AuthUser, "考试记录详情")
		examGroup.Register("GET", "/hisrory/answer/:id", api.GetAnswerHistoryDetail, model.AuthUser, "获取考试做题详情")
	}
}

func (api *ExamAPI) GetExamList(c *gin.Context) {
	var req model.ReqGetExams
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误: "+err.Error())
		return
	}
	userID := c.GetInt64("userId")
	if userID == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}
	req.UserID = userID
	resp, total, err := examService.GetExamsList(c, req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", gin.H{
		"list":  resp,
		"total": total,
	})
}

func (api *ExamAPI) GetPaper(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的试卷ID")
		return
	}
	resp, err := paperService.GetPaperDetail(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	// 隐藏题目答案
	for i := range resp.PaperQuestions {
		var qcontent map[string]any
		err = json.Unmarshal([]byte(resp.PaperQuestions[i].Content), &qcontent)
		if err != nil {
			libs.Error(c, "解析题目内容失败: "+err.Error())
			return
		}

		delete(qcontent, "answer")
		newContent, err := json.Marshal(qcontent)
		if err != nil {
			libs.Error(c, "序列化题目内容失败: "+err.Error())
			return
		}
		resp.PaperQuestions[i].Content = string(newContent)
	}

	libs.Success(c, "查询成功", resp)
}

// StartExam 开始考试
func (api *ExamAPI) StartExam(c *gin.Context) {
	userID := c.GetInt64("userId")
	var req struct {
		ExamID int64 `json:"id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if userID == 0 || req.ExamID == 0 {
		libs.Error(c, "参数错误")
		return
	}

	id, err := examProgressService.CreateExamProgress(userID, req.ExamID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "考试已创建", gin.H{"id": id})
}

// SubmitExam 提交考试答案
func (api *ExamAPI) SubmitExam(c *gin.Context) {
	var req struct {
		ProgressID   int64              `json:"id"`
		Answers      []model.ExamAnswer `json:"answers"`
		SubmitStatue string             `json:"submit_status" binding:"required,oneof=commit terminate"` // commit / terminate
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if req.ProgressID == 0 {
		libs.Error(c, "无效的考试进度ID")
		return
	}

	progress, err := examProgressService.SubmitExam(req.ProgressID, req.Answers, req.SubmitStatue)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	go func(progress model.ExamProgress) {
		examProgressService.CheckAndFinishExam(progress.ExamID)
		claims, err := auth.GetUserClaims(c)
		if err != nil {
			slog.Error("获取用户信息失败", "error", err)
			return
		}
		userProgress, err := examProgressService.QueryExamProgressAndAnswers(req.ProgressID)
		if err != nil {
			slog.Error("查询失败", "error", err)
			return
		}

		// 同步数据
		err = eventSync.NewEventSyncBuilder(claims.ID).
			Operator(claims.ID).
			SyncClient().
			EventType(eventSync.ExamSyncType).
			Data(userProgress).
			Save()
		if err != nil {
			slog.Error("插入同步记录失败", "error", err)
			return
		}
	}(progress)

	libs.Success(c, "提交成功", gin.H{"progress_status": progress.Status})
}

// GetExamDetail 获取考试进度详情
func (api *ExamAPI) GetExamDetail(c *gin.Context) {
	progressID := cast.ToInt64(c.Param("id"))
	if progressID == 0 {
		libs.Error(c, "参数错误")
		return
	}

	progress, err := examProgressService.GetExamProgress(progressID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", progress)
}

func (api *ExamAPI) GetExamHistory(c *gin.Context) {
	claims, err := auth.GetUserClaims(c)
	if err != nil {
		libs.Error(c, "用户ID不能为空")
		return
	}
	res, err := examProgressService.GetExamHistory(claims.ID)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	libs.Success(c, "查询成功", res)
}

func (api *ExamAPI) GetExamHistoryDetail(c *gin.Context) {
	progressID := cast.ToInt64(c.Param("id"))
	if progressID == 0 {
		libs.Error(c, "参数错误")
		return
	}

	userProgress, err := examProgressService.QueryExamProgressAndAnswers(progressID)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	// 获取题目
	paper, err := paperService.GetPaperDetail(userProgress.ExamProgress.PaperID)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	for i := range paper.PaperQuestions {
		// 隐藏题目内容，避免数据过大
		paper.PaperQuestions[i].Content = ""
		paper.PaperQuestions[i].Answer = ""
	}

	libs.Success(c, "查询成功", gin.H{
		"exam":            userProgress.Exam,
		"paper":           paper.Papers,
		"paper_questions": paper.PaperQuestions,
		"progress":        userProgress.ExamProgress,
	})
}

// GetAnswerHistoryDetail 获取考试做题详情
func (api *ExamAPI) GetAnswerHistoryDetail(c *gin.Context) {
	progressID := cast.ToInt64(c.Param("id"))
	if progressID == 0 {
		libs.Error(c, "参数错误")
		return
	}

	questionID := cast.ToInt64(c.Query("question_id"))
	if questionID == 0 {
		libs.Error(c, "参数错误")
		return
	}

	answer, err := examProgressService.GetAnswerHistoryDetail(progressID, questionID)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	var questionContent map[string]any
	err = json.Unmarshal([]byte(answer.PaperQuestion.Content), &questionContent)
	if err != nil {
		libs.Error(c, "解析题目内容失败: "+err.Error())
		return
	}
	delete(questionContent, "answer")
	newContent, err := json.Marshal(questionContent)
	if err != nil {
		libs.Error(c, "序列化题目内容失败: "+err.Error())
		return
	}
	answer.PaperQuestion.Content = string(newContent)
	answer.PaperQuestion.Answer = ""

	libs.Success(c, "查询成功", answer)
}
