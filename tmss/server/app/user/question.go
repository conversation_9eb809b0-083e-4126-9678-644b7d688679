package user

import (
	"tms/libs"
	"tms/model"

	"github.com/gin-gonic/gin"
)

type QuestionAPI struct{}

func init() {
	api := &QuestionAPI{}
	usersV1 := GetVersion()
	questionGroup := usersV1.Group("question", "试题接口")
	{
		questionGroup.Register("GET", "/list", api.GetQuestionList, model.AuthUser, "获取试题列表")
	}
}

func (api *QuestionAPI) GetQuestionList(c *gin.Context) {
	var req model.ReqQuestionSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	res, err := questionService.GetQuestionsByCourseOrChapter(req)
	if err != nil {
		libs.Error(c, "查询失败: "+err.<PERSON>rror())
		return
	}

	libs.Success(c, "查询成功", res)
}
