package question

import (
	"testing"
	"tms/model"
	"tms/pkg/db"
	"tms/services/tech"
	"tms/test/testdb"
)

func TestGenerateQuestions(t *testing.T) {
	pgdb, err := testdb.InitPostgreSQL()
	if err != nil {
		t.Fatalf("Failed to connect to database: %v", err)
	}
	db.DB = pgdb

	qs := tech.NewQuestionService()

	// 生成100道判断题
	judgeQuestions := []struct {
		title   string
		content string
		answer  string
	}{
		{"计算机基础知识1", "计算机的中央处理器CPU主要由运算器和控制器组成。", "正确"},
		{"计算机基础知识2", "编译程序是操作系统的主要功能之一。", "错误"},
		{"计算机基础知识3", "在计算机中，1KB等于1000字节。", "错误"},
		{"计算机基础知识4", "TCP/IP协议属于传输层。", "正确"},
		{"计算机基础知识5", "Java是面向对象编程语言。", "正确"},
		{"数据结构1", "栈的特点是后进先出。", "正确"},
		{"数据结构2", "队列的特点是后进先出。", "错误"},
		{"数据结构3", "二叉树只有两种遍历方式。", "错误"},
		{"数据结构4", "哈希表的平均查找时间复杂度是O(1)。", "正确"},
		{"数据结构5", "链表相比数组在插入删除操作上效率更高。", "正确"},
		{"算法基础1", "冒泡排序的时间复杂度是O(n²)。", "正确"},
		{"算法基础2", "快速排序的平均时间复杂度是O(n²)。", "错误"},
		{"算法基础3", "二分查找要求数组必须是有序的。", "正确"},
		{"算法基础4", "递归算法必须具备终止条件。", "正确"},
		{"算法基础5", "动态规划的核心思想是分治法。", "错误"},
		{"数据库1", "SQL中用于查询的关键字是SELECT。", "正确"},
		{"数据库2", "主键的作用是唯一标识记录。", "正确"},
		{"数据库3", "外键用于建立表间关联关系。", "正确"},
		{"数据库4", "事务的ACID特性中A代表可用性。", "错误"},
		{"数据库5", "索引的主要作用是提高查询速度。", "正确"},
		{"网络基础1", "HTTP协议默认端口号是80。", "正确"},
		{"网络基础2", "HTTPS协议默认端口号是80。", "错误"},
		{"网络基础3", "DNS的作用是域名解析。", "正确"},
		{"网络基础4", "IP地址分为三类。", "错误"},
		{"网络基础5", "子网掩码的作用是划分网络。", "正确"},
		{"编程语言1", "Java是面向对象编程语言。", "正确"},
		{"编程语言2", "Python的特点是语法复杂难懂。", "错误"},
		{"编程语言3", "C语言属于面向对象编程语言。", "错误"},
		{"编程语言4", "JavaScript主要用于网页开发。", "正确"},
		{"编程语言5", "SQL是结构化查询语言。", "正确"},
		{"软件工程1", "软件生命周期只包括编码和测试两个阶段。", "错误"},
		{"软件工程2", "瀑布模型的特点是线性顺序开发。", "正确"},
		{"软件工程3", "敏捷开发的核心理念是快速迭代。", "正确"},
		{"软件工程4", "单元测试的目的是测试整个系统。", "错误"},
		{"软件工程5", "版本控制的作用是管理代码变更。", "正确"},
		{"操作系统1", "进程是资源分配的基本单位。", "正确"},
		{"操作系统2", "死锁产生需要满足四个必要条件。", "正确"},
		{"操作系统3", "虚拟内存的作用是扩大内存空间。", "正确"},
		{"操作系统4", "文件系统的作用是管理存储设备上的文件。", "正确"},
		{"操作系统5", "中断的作用是处理异步事件。", "正确"},
		{"计算机组成1", "冯·诺依曼结构的特点是程序和数据分别存储。", "错误"},
		{"计算机组成2", "Cache的作用是提高CPU访问内存的速度。", "正确"},
		{"计算机组成3", "总线的作用是连接计算机各个部件。", "正确"},
		{"计算机组成4", "指令周期只包括取指和执行两个阶段。", "错误"},
		{"计算机组成5", "存储器按访问速度从快到慢的顺序是寄存器、Cache、内存、外存。", "正确"},
		{"信息安全1", "对称加密的特点是加密和解密使用同一密钥。", "正确"},
		{"信息安全2", "非对称加密的优势是密钥分发更安全。", "正确"},
		{"信息安全3", "数字签名的作用包括身份认证和完整性验证。", "正确"},
		{"信息安全4", "防火墙的作用是网络访问控制。", "正确"},
		{"信息安全5", "计算机病毒具有传染性、破坏性和隐蔽性特征。", "正确"},
		{"人工智能1", "机器学习包括监督学习、无监督学习和强化学习三种类型。", "正确"},
		{"人工智能2", "神经网络的基本单元是神经元。", "正确"},
		{"人工智能3", "深度学习的特点是使用单层神经网络。", "错误"},
		{"人工智能4", "决策树的优势是可解释性强。", "正确"},
		{"人工智能5", "聚类算法属于监督学习。", "错误"},
		{"Web开发1", "HTML的作用是定义网页结构。", "正确"},
		{"Web开发2", "CSS的作用是控制网页样式。", "正确"},
		{"Web开发3", "JavaScript的作用是实现网页交互。", "正确"},
		{"Web开发4", "HTTP只有GET和POST两种请求方法。", "错误"},
		{"Web开发5", "RESTful API的特点包括无状态和统一接口。", "正确"},
		{"移动开发1", "Android开发主要使用Java或Kotlin语言。", "正确"},
		{"移动开发2", "iOS开发只能使用Objective-C语言。", "错误"},
		{"移动开发3", "跨平台开发的优势是一次开发可在多平台运行。", "正确"},
		{"移动开发4", "响应式设计的目的是适配不同屏幕尺寸。", "正确"},
		{"移动开发5", "移动应用的生命周期只包括创建和销毁两个阶段。", "错误"},
		{"云计算1", "云计算的服务模式包括IaaS、PaaS、SaaS。", "正确"},
		{"云计算2", "虚拟化技术的优势是提高资源利用率。", "正确"},
		{"云计算3", "容器技术的特点是轻量级和可移植。", "正确"},
		{"云计算4", "微服务架构的优势包括松耦合和独立部署。", "正确"},
		{"云计算5", "负载均衡的作用是分散请求压力。", "正确"},
		{"大数据1", "大数据的特征可以用4V来概括：Volume、Velocity、Variety、Value。", "正确"},
		{"大数据2", "Hadoop的核心组件是HDFS和MapReduce。", "正确"},
		{"大数据3", "NoSQL数据库都是关系型数据库。", "错误"},
		{"大数据4", "数据挖掘的目的是发现数据中的模式和规律。", "正确"},
		{"大数据5", "流处理的特点是批量处理历史数据。", "错误"},
		{"区块链1", "区块链的特点包括去中心化和不可篡改。", "正确"},
		{"区块链2", "比特币使用的共识机制是权益证明。", "错误"},
		{"区块链3", "智能合约的作用是自动执行合约条款。", "正确"},
		{"区块链4", "哈希函数具有单向性和雪崩效应特性。", "正确"},
		{"区块链5", "默克尔树的作用是验证数据完整性。", "正确"},
		{"项目管理1", "项目管理的三要素是时间、成本、质量。", "正确"},
		{"项目管理2", "敏捷开发的迭代周期通常是1-4周。", "正确"},
		{"项目管理3", "风险管理只需要识别和分析两个步骤。", "错误"},
		{"项目管理4", "需求分析的重要性在于确定项目目标和范围。", "正确"},
		{"项目管理5", "团队协作的关键是沟通和协调。", "正确"},
		{"数学基础1", "线性代数在计算机中可应用于图形变换和机器学习。", "正确"},
		{"数学基础2", "概率论在AI中的作用是进行不确定性建模。", "正确"},
		{"数学基础3", "离散数学是计算机科学的重要数学基础。", "正确"},
		{"数学基础4", "微积分在优化中不能用于求解最值问题。", "错误"},
		{"数学基础5", "统计学在数据分析中的作用是数据描述和推断。", "正确"},
		{"系统设计1", "高可用系统的设计原则包括冗余和故障转移。", "正确"},
		{"系统设计2", "缓存的作用是提高系统性能。", "正确"},
		{"系统设计3", "分布式系统只需要考虑一致性问题。", "错误"},
		{"系统设计4", "API设计的原则包括简洁、一致、可扩展。", "正确"},
		{"系统设计5", "监控系统的重要性在于及时发现和解决问题。", "正确"},
		{"技术趋势1", "物联网的核心技术包括传感器、通信、数据处理。", "正确"},
		{"技术趋势2", "边缘计算的优势是低延迟和减少带宽占用。", "正确"},
		{"技术趋势3", "5G技术的特点是高速率、低延迟、大连接。", "正确"},
		{"技术趋势4", "量子计算的潜力是实现指数级计算能力提升。", "正确"},
		{"技术趋势5", "AR/VR技术只能应用于娱乐领域。", "错误"},
		{"职业发展1", "程序员的核心技能包括逻辑思维和学习能力。", "正确"},
		{"职业发展2", "技术选型只需要考虑性能因素。", "错误"},
		{"职业发展3", "代码质量的重要性体现在可读性和可维护性。", "正确"},
		{"职业发展4", "持续学习的必要性源于技术的快速发展。", "正确"},
		{"职业发展5", "团队合作的价值在于提高效率和知识共享。", "正确"},
	}

	for i, q := range judgeQuestions {
		if q.answer == "正确" {
			q.answer = `"true"`
		} else {
			q.answer = `"false"`
		}
		qs.CreateQuestionDraft(model.ReqQuestionUpdate{
			Questions: model.Questions{
				Title:        q.title,
				QuestionType: model.QuestionTypeJudge,
				Content:      q.content,
				Answer:       q.answer,
				Score:        3,
				Minutes:      1,
				Options:      "",
			},
			CoursewareIDs: []int64{1},
			ChapterIDs:    []int64{1},
		}, 2)
		t.Logf("创建判断题 %d: %s", i+1, q.title)
	}

}
