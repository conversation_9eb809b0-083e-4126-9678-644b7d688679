package testdb

import (
	"fmt"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func InitPostgreSQL() (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		"localhost",
		"5442",
		"root",
		"root",
		"tms",
		"")

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	return db, nil
}

// SetupDB initializes a new in-memory SQLite database for testing.
func SetupDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open("file::memory:?cache=shared&_auto_increment=true"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // Silent logger for tests
	})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// AutoMigrate all models
	err = db.AutoMigrate()
	if err != nil {
		log.Fatalf("Failed to auto migrate models: %v", err)
	}

	return db
}
